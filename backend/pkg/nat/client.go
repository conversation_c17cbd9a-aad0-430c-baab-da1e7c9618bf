package nat

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	waProto "go.mau.fi/whatsmeow/binary/proto"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"

	"github.com/nats-io/nats.go"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"google.golang.org/protobuf/proto"
)

var (
	nc         *nats.Conn
	err        error
	WaConnects = make(map[string]*whatsmeow.Client)
	waDebug    = flag.String("wadebug", "", "Enable whatsmeow debug (INFO or DEBUG)")
)

func Connect(nat config.Nats) {
	nc, err = nats.Connect(fmt.Sprintf("nats://%v:%v", nat.Host, nat.Port), nats.Name("vatansoft-wp-api"))
	if err != nil {
		log.Fatalf("Can't connect to nats: %s", err)
	}
	SendSingleMessage()
	FailedSaveDatabase()
}

func Client() *nats.Conn {
	return nc
}

func SaveMessage() {
	nc.Subscribe("save-message", func(msg *nats.Msg) {
		var (
			message = entities.Message{}
			req     dtos.AddNatsMessage
			db      = database.DBClient()
		)
		err := json.Unmarshal(msg.Data, &req)
		if err != nil {
			return
		}
		message.Mapper(req)
		message.IsSended = true
		err = db.Save(&message).Error
		req.MsgId = message.ID
		if err != nil {

			return
		}

		data, err := json.Marshal(req)
		if err != nil {

			log.Println("cannot marshal data, err: " + err.Error())
			return
		}
		err = nc.Publish("single-message", data)
		if err != nil {
			log.Println("send callback error: " + err.Error())
		}

	})
	if err == nil {
		log.Println("save message db nat subscribed")
	}
}

func SendSingleMessage() {
	nc.Subscribe("single-message", func(msg *nats.Msg) {
		var (
			req    dtos.AddNatsMessage
			ctx    = context.Background()
			client *whatsmeow.Client
			isLog  bool
		)
		err := json.Unmarshal(msg.Data, &req)
		if err != nil {
			return
		}

		var num string
		if !strings.HasPrefix(req.To, "+") {
			num = "+" + req.To
		}
		isValid, err := utils.ValidatePhoneNumber(num)
		if err != nil || !isValid {
			return
		}

		client = WaConnects[req.RegId]
		if client == nil {
			client, isLog = CheckDevice(ctx, req.JID, req.RegId)
			if !isLog {
				return
			}
			WaConnects[req.JID] = client
		}
		//TODO: send message added
	})
	if err == nil {
		log.Println("single message nat subscribed")
	}
}

func FailedSaveDatabase() {
	nc.Subscribe("save-failed", func(msg *nats.Msg) {
		var (
			message = entities.Message{}
			db      = database.DBClient()
		)
		err := json.Unmarshal(msg.Data, &message)
		if err != nil {
			return
		}
		err = db.Save(&message).Error
		if err != nil {
			return
		}
	})
	if err == nil {
		log.Println("failed save db nat subscribed")
	}
}

func CheckDevice(ctx context.Context, jid string, regId string) (*whatsmeow.Client, bool) {
	w := wrapper.WpClient()
	senderArr := strings.Split(jid, "@")
	sender := types.NewJID(senderArr[0], types.DefaultUserServer)
	device, err := w.MContainer.GetDevice(sender)
	if err != nil {
		return nil, false
	}
	if device == nil {
		return nil, false
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)
	err = client.Connect()
	if err != nil {
		return nil, false
	}
	WaConnects[regId] = client
	return client, true
}

func SendMessage(ctx context.Context, req dtos.AddNatsMessage) error {
	db := database.DBClient()
	client := WaConnects[req.RegId]
	if client == nil {
		log.Println("error send message: client isn't logged in")
		err = db.Model(&entities.Message{}).Where("id = ?", req.MsgId).Update("status", consts.Status[consts.Failed]).Error
		//TODO: send callback for failed message save logs
		if err != nil {
			return err
		}
		return errors.New("device isn't logged in")
	}
	client.Connect()

	message := &waProto.Message{
		Conversation: proto.String(req.Message),
	}

	to := types.NewJID(req.To, types.DefaultUserServer)
	_, err = client.SendMessage(ctx, to, message)
	if err != nil {
		return err
	}
	err = db.Model(&entities.Message{}).Where("id = ?", req.MsgId).Update("status", consts.Status[consts.Delivered]).Error
	if err != nil {
		return err
	}
	log.Println("message sent successfully", req.To, req.Message)

	return nil
}

func SubscribeDevicePresences(ctx context.Context, jId string) (string, error) {
	fmt.Println("SubscribeDevicePresences jId:", jId)
	w := wrapper.WpClient()
	pjid, _ := types.ParseJID(jId)
	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jId).
		First(&session).Error; err != nil {
		return "", err
	}

	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	client := WaConnects[regId]

	// Eğer client yoksa yeni oluştur
	if client == nil {
		clientLog := waLog.Stdout("Client", *waDebug, true)
		client = whatsmeow.NewClient(device, clientLog)

		err = client.Connect()
		if err != nil {
			return "", err
		}

		client.AddEventHandler(eventHandler)
		WaConnects[regId] = client
	}

	client.SendPresence(types.PresenceAvailable)

	var subscribes []entities.SessionSubscription
	db.Where("session_id = ?", session.ID).Find(&subscribes)

	for _, v := range subscribes {
		subJid := v.Phone + "@s.whatsapp.net"
		parsedJID, _ := types.ParseJID(subJid)
		err = client.SubscribePresence(parsedJID)
		if err != nil {
			fmt.Printf("Error subscribing to %s: %v\n", subJid, err)
			continue // Bir subscribe hatası diğerlerini etkilemesin
		}
		fmt.Println("SubscribePresence subJid:", subJid)
	}

	return "subscribed", nil
}

func eventHandler(evt interface{}) {
	switch v := evt.(type) {
	case *events.Presence:

		var subscribes []entities.SessionSubscription
		db := database.DBClient()

		phone, _ := ParseNumberFromJID(v.From.String())

		db.Where("phone = ?", phone).Find(&subscribes)
		status := consts.PresenceOnline
		if v.Unavailable {
			status = consts.PresenceOffline
		}

		for _, sub := range subscribes {
			var presence, lastPresence entities.Presence
			presence.SessionId = sub.SessionID.String()
			presence.Status = status
			if status == consts.PresenceOffline {
				presence.LastSeen = time.Now()
			}
			presence.SubscribePhone = sub.Phone

			db.Where("subscribe_phone = ?", sub.Phone).Order("created_at desc").First(&lastPresence)
			if lastPresence.Status == status {
				continue
			}
			db.Create(&presence)
		}

		fmt.Println("Presence event running", v.LastSeen)
		jid := v.From.String()

		fmt.Println("Presence From", jid)
		fmt.Println("Presence Last Seen", v.LastSeen)
		fmt.Println("Presence Available", status)

		/*case *events.Message:
		fmt.Println("Message event running", v.Message)
		fmt.Println("Message From", v.Info.Chat.User)*/
	}
}

func ParseNumberFromJID(jid string) (string, error) {
	parts := strings.Split(jid, "@")
	if len(parts) < 2 {
		return "", fmt.Errorf("geçersiz JID formatı: %s", jid)
	}
	return parts[0], nil
}

func AddNewSubscription(ctx context.Context, jId string, phoneToSubscribe string) error {
	w := wrapper.WpClient()
	pjid, _ := types.ParseJID(jId)
	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return err
	}
	if device == nil {
		return errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jId).
		First(&session).Error; err != nil {
		return err
	}

	// Mevcut client'ı kullan
	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	client := WaConnects[regId]

	if client == nil {
		// Client yoksa önce SubscribeDevicePresences çağrılmalı
		return errors.New("client not initialized, call SubscribeDevicePresences first")
	}

	// Yeni subscribe ekle
	subJid := phoneToSubscribe + "@s.whatsapp.net"
	parsedJID, _ := types.ParseJID(subJid)
	err = client.SubscribePresence(parsedJID)
	if err != nil {
		return err
	}

	// Veritabanına kaydet
	subscribe := entities.SessionSubscription{
		SessionID:      session.ID,
		Phone:          phoneToSubscribe,
		JID:            subJid,
		Active:         true,
		EventHandlerId: 0, // EventHandlerId client'a bağlı, tek bir handler var
	}

	return db.Create(&subscribe).Error
}
