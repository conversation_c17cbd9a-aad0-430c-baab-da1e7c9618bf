package wrapper

import (
	"context"
	"errors"
	"fmt"
	"image"
	"image/color"
	"strconv"
	"strings"
	"time"

	"github.com/makiuchi-d/gozxing"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/proxy"

	"github.com/labstack/gommon/log"
	_ "github.com/lib/pq"

	_log "log"

	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

type Client struct {
	MContainer *sqlstore.Container
	// devices    []*store.Device
	// activeClients stores active WhatsApp clients by JID
	activeClients map[string]*whatsmeow.Client
}

var WPW *Client
var GenerateImageOnLogin = false

func Init(db config.Database) {
	fmt.Println("wrapper init...")
	url := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", db.User, db.Pass, db.Host, db.Port, db.Name)
	container, err := sqlstore.New("postgres", url, nil)
	if err != nil {
		fmt.Println("wrapper init error:", err.Error())
		panic(err)
	}
	log.Info("wrapper init successfully...")
	WPW = &Client{
		MContainer:    container,
		activeClients: make(map[string]*whatsmeow.Client),
	}
}

func (w *Client) GetCode(ctx context.Context, req dtos.GetCodeReq) (dtos.GetCodeResp, error) {
	var (
		resp dtos.GetCodeResp
	)
	//find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).Where("id = ?", req.SessionID).First(&session).Error; err != nil {
		return resp, errors.New("session not found for id: " + req.SessionID)
	}

	device := w.MContainer.NewDevice()
	clientLog := waLog.Stdout("Client", "DEBUG", true)

	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return resp, errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}

	if err := client.Connect(); err != nil {
		return resp, errors.New("client connect error: " + err.Error())
	}

	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	code, err := client.PairPhone(req.Phone, true, whatsmeow.PairClientChrome, "Chrome (Linux)")
	if err != nil {
		return resp, errors.New("code isn't generate,err: " + err.Error())
	}

	resp.Code = code
	resp.RegId = string(regId)

	loginCode := entities.LoginCode{
		Code:           code,
		PhoneNumber:    req.Phone,
		RegistrationID: string(regId),
		Status:         1,
		IP:             req.IP,
		SessionID:      req.SessionID,
	}

	err = db.Create(&loginCode).Error
	if err != nil {
		return resp, err
	}

	return resp, nil
}

func (w *Client) GetQr(ctx context.Context, req dtos.GetCodeReq) (string, string, error) {
	var err error
	device := w.MContainer.NewDevice()
	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(device, clientLog)
	s_proxy, err := proxy.SetUrlWithoutSession(ctx, req.IP, req.Phone)
	if err != nil {
		return "", string(regId), errors.New("proxy error: " + err.Error())
	}
	client.SetProxy(s_proxy)
	qrChan, err := client.GetQRChannel(context.Background())
	if err != nil {
		return "", string(regId), err
	}
	err = client.Connect()
	if err != nil {
		return "", string(regId), err
	}
	for evt := range qrChan {
		if evt.Event == "code" {
			return evt.Code, string(regId), err
		} else {
			fmt.Print("Bağlantı bulunamadı2")
		}
	}
	return "", "", nil

}

func (w *Client) CheckDevice(ctx context.Context, jid string, regId string) (*whatsmeow.Client, bool) {

	senderArr := strings.Split(jid, "@")
	sender := types.NewJID(senderArr[0], types.DefaultUserServer)
	device, err := w.MContainer.GetDevice(sender)
	if err != nil {
		return nil, false
	}
	if device == nil {
		return nil, false
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("reg_id = ?", regId).
		First(&session).Error; err != nil {
		return nil, false
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return nil, false
		}
		client.SetProxy(s_proxy)
	}

	client.AddEventHandler(eventHandler)
	err = client.Connect()
	if err != nil {
		return nil, false
	}

	// Store the client in our active clients map
	w.activeClients[jid] = client

	session.JID = jid
	err = db.Updates(&session).Error
	if err != nil {
		_log.Println("Error updating session:", err.Error())
		return nil, false
	}
	return client, true
}

func (w *Client) GetProfilePhoto(ctx context.Context, jid string) (string, error) {

	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jid).
		First(&session).Error; err != nil {
		return "", err
	}

	_log.Println("session name: ", session.ProxyID)

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return "", errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}

	err = client.Connect()
	if err != nil {
		return "", err
	}

	picture, err := client.GetProfilePictureInfo(pjid.ToNonAD(), &whatsmeow.GetProfilePictureParams{
		Preview:     false,
		ExistingID:  "",
		IsCommunity: false,
	})

	if err != nil {
		return "", err
	}
	return picture.URL, nil
}

// GetProfilePhotoByPhone gets profile photo for a specific phone number using a device
func (w *Client) GetProfilePhotoByPhone(ctx context.Context, jid string, phoneNumber string) (string, error) {
	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jid).
		First(&session).Error; err != nil {
		return "", err
	}

	_log.Println("session name: ", session.ProxyID)

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return "", errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}

	err = client.Connect()
	if err != nil {
		return "", err
	}

	// Parse the phone number to JID
	targetJID, err := types.ParseJID(phoneNumber + "@s.whatsapp.net")
	if err != nil {
		return "", errors.New("invalid phone number format")
	}

	picture, err := client.GetProfilePictureInfo(targetJID, &whatsmeow.GetProfilePictureParams{
		Preview:     false,
		ExistingID:  "",
		IsCommunity: false,
	})

	if err != nil {
		return "", err
	}
	return picture.URL, nil
}

func (w *Client) SubscribePresence(ctx context.Context, jid, subscribePhone string) (uint32, error) {

	pjid, _ := types.ParseJID(jid)

	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return 0, err
	}
	if device == nil {
		return 0, errors.New("device not found")
	}

	fmt.Println("device id", device.RegistrationID)

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jid).
		First(&session).Error; err != nil {
		return 0, err
	}

	regId := strconv.Itoa(int(device.RegistrationID))
	subJid := subscribePhone + "@s.whatsapp.net"

	// Check if we have an active client for this JID
	if existingClient, exists := w.activeClients[jid]; exists && existingClient.IsConnected() {
		fmt.Println("Using existing client for subscription:", subscribePhone)

		// Check if subscription already exists for this phone
		var existingSubscribe entities.Subscribe
		err = db.Where("reg_id = ? AND phone = ?", regId, subscribePhone).First(&existingSubscribe).Error
		if err == nil {
			fmt.Println("Subscription already exists for phone:", subscribePhone)
			return existingSubscribe.EventHandlerId, nil
		}

		// Add new subscription to existing client
		parsedJID, _ := types.ParseJID(subJid)
		err = existingClient.SubscribePresence(parsedJID)
		if err != nil {
			fmt.Println("SubscribePresence error with existing client:", err.Error())
			return 0, err
		}

		// Create new subscription record with the same event handler ID as the client
		// We'll use a dummy event handler ID since we're reusing the client
		var firstSubscribe entities.Subscribe
		db.Where("reg_id = ?", regId).First(&firstSubscribe)

		subscribe := entities.Subscribe{
			RegId:          regId,
			Phone:          subscribePhone,
			JID:            subJid,
			EventHandlerId: firstSubscribe.EventHandlerId, // Use same event handler ID
		}
		db.Create(&subscribe)
		return firstSubscribe.EventHandlerId, nil
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return 0, errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}

	eventHandlerId := client.AddEventHandler(eventHandler)
	err = client.Connect()
	if err != nil {
		return eventHandlerId, err
	}

	// Store the client in our active clients map
	w.activeClients[jid] = client

	client.SendPresence(types.PresenceAvailable)

	fmt.Println("SubscribePresence subJid:", subJid)
	parsedJID, _ := types.ParseJID(subJid)

	fmt.Println("SubscribePresence parsedJID:", parsedJID)
	err = client.SubscribePresence(parsedJID)
	if err != nil {
		fmt.Println("SubscribePresence error:", err.Error())
		return eventHandlerId, err
	}

	subscribe := entities.Subscribe{
		RegId:          regId,
		Phone:          subscribePhone,
		JID:            subJid,
		EventHandlerId: eventHandlerId,
	}
	db.Create(&subscribe)
	return eventHandlerId, nil
}

func (w *Client) GetPresence(ctx context.Context) {
}

func (w *Client) RemovePresenceSubscription(ctx context.Context, jid, subscribePhone string, eventHandlerID uint32) error {
	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return err
	}
	if device == nil {
		return errors.New("device not found")
	}

	db := database.DBClient()
	regId := strconv.Itoa(int(device.RegistrationID))

	// Check if we have an active client for this JID
	if existingClient, exists := w.activeClients[jid]; exists {
		// Check if this was the last subscription for this device
		var remainingSubscriptions []entities.Subscribe
		db.Where("reg_id = ? AND phone != ?", regId, subscribePhone).Find(&remainingSubscriptions)

		// If no more subscriptions for this device, remove event handler and disconnect client
		if len(remainingSubscriptions) == 0 {
			success := existingClient.RemoveEventHandler(eventHandlerID)
			if success {
				fmt.Println("Successfully removed event handler ID:", eventHandlerID)
			} else {
				fmt.Println("Failed to remove event handler ID:", eventHandlerID)
			}
			existingClient.Disconnect()
			delete(w.activeClients, jid)
			fmt.Println("Disconnected and removed client for JID:", jid)
		} else {
			fmt.Println("Keeping client active, remaining subscriptions:", len(remainingSubscriptions))
		}
	}

	// Remove from database
	err = db.Where("reg_id = ? AND phone = ?", regId, subscribePhone).Delete(&entities.Subscribe{}).Error
	if err != nil {
		fmt.Println("Database delete error:", err.Error())
		return err
	}

	fmt.Println("Successfully removed presence subscription for:", subscribePhone)
	return nil
}

func BitMatrixToImage(bitMatrix *gozxing.BitMatrix) *image.Gray {
	width := bitMatrix.GetWidth()
	height := bitMatrix.GetHeight()
	img := image.NewGray(image.Rect(0, 0, width, height))
	for x := 0; x < width; x++ {
		for y := 0; y < height; y++ {
			if bitMatrix.Get(x, y) {
				img.Set(x, y, color.Black)
			} else {
				img.Set(x, y, color.White)
			}
		}
	}
	return img
}

/*

func (w *Client) GetPresence(ctx context.Context, jid string) (string, error) {

	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("device not found")
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)
	err = client.Connect()
	if err != nil {
		return "", err
	}

	picture, err := client.SubscribePresence(pjid.ToNonAD(), &whatsmeow.GetProfilePictureParams{
		Preview:     false,
		ExistingID:  "",
		IsCommunity: false,
	})

	if err != nil {
		return "", err
	}
	return picture.URL, nil
}*/

// CleanupClientForJID removes and disconnects a client for a specific JID
func (w *Client) CleanupClientForJID(jid string) {
	if client, exists := w.activeClients[jid]; exists {
		client.Disconnect()
		delete(w.activeClients, jid)
		fmt.Println("Cleaned up client for JID:", jid)
	}
}

// CleanupAllClients disconnects and removes all active clients
func (w *Client) CleanupAllClients() {
	for jid, client := range w.activeClients {
		client.Disconnect()
		delete(w.activeClients, jid)
		fmt.Println("Cleaned up client for JID:", jid)
	}
}

func WpClient() *Client {
	return WPW
}

func eventHandler(evt interface{}) {
	switch v := evt.(type) {
	case *events.Presence:

		fmt.Println("Presence Last Seen", v.LastSeen)
		//fmt.Println("Presence Available", status)

		var subscribes []entities.Subscribe
		db := database.DBClient()

		phone, _ := ParseNumberFromJID(v.From.String())

		db.Where("phone = ?", phone).Find(&subscribes)
		status := consts.PresenceOnline
		if v.Unavailable {
			status = consts.PresenceOffline
		}

		for _, sub := range subscribes {
			var presence, lastPresence entities.Presence
			presence.SessionId = sub.RegId // Use RegId as session identifier
			presence.Status = status
			if status == consts.PresenceOffline {
				presence.LastSeen = time.Now()
			}
			presence.SubscribePhone = sub.Phone

			db.Where("subscribe_phone = ?", sub.Phone).Debug().Order("created_at desc").First(&lastPresence)
			if lastPresence.Status == status {
				continue
			}
			db.Create(&presence)
		}

		fmt.Println("Presence event running", v.LastSeen)
		jid := v.From.String()

		fmt.Println("Presence From", jid)
		fmt.Println("Presence Last Seen", v.LastSeen)
		fmt.Println("Presence Available", status)

		/*case *events.Message:
		fmt.Println("Message event running", v.Message)
		fmt.Println("Message From", v.Info.Chat.User)*/
	}
}

func ParseNumberFromJID(jid string) (string, error) {
	parts := strings.Split(jid, "@")
	if len(parts) < 2 {
		return "", fmt.Errorf("geçersiz JID formatı: %s", jid)
	}
	return parts[0], nil
}
